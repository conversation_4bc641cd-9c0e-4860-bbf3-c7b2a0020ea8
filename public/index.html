<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ForaChat - Delayed Streaming Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .chat-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .chat-header {
            background: #007AFF;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 18px;
            max-width: 70%;
            animation: fadeIn 0.3s ease-in;
        }
        
        .message.user {
            background: #007AFF;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message.assistant {
            background: #e9ecef;
            color: #333;
        }
        
        .message.system {
            background: #fff3cd;
            color: #856404;
            text-align: center;
            font-style: italic;
            max-width: 100%;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            max-width: 100%;
        }
        
        .character-name {
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 4px;
            opacity: 0.8;
        }
        
        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #eee;
        }
        
        .chat-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .chat-input:focus {
            border-color: #007AFF;
        }
        
        .chat-input:disabled {
            background: #f8f9fa;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px 20px;
            background: #e3f2fd;
            color: #1976d2;
            text-align: center;
            font-size: 14px;
        }
        
        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: #e9ecef;
            border-radius: 18px;
            max-width: 70%;
            margin-bottom: 15px;
        }
        
        .typing-indicator.show {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }
        
        .typing-dots {
            display: inline-block;
        }
        
        .typing-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        
        .skills {
            margin-top: 10px;
            padding: 8px 12px;
            background: #f0f8ff;
            border-radius: 12px;
            font-size: 0.9em;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>ForaChat - Delayed Streaming Test</h1>
            <div id="connection-status" class="status">Connecting...</div>
        </div>
        
        <div class="chat-messages" id="messages">
            <div class="message system">
                Welcome! Type a message to start chatting. Messages will appear with realistic delays.
            </div>
        </div>
        
        <div class="typing-indicator" id="typing-indicator">
            <span class="typing-dots">Typing</span>
        </div>
        
        <div class="chat-input-container">
            <input 
                type="text" 
                id="message-input" 
                class="chat-input" 
                placeholder="Type your message here..." 
                disabled
            >
        </div>
    </div>

    <script>
        class DelayedChatUI {
            constructor() {
                this.ws = null;
                this.messageInput = document.getElementById('message-input');
                this.messagesContainer = document.getElementById('messages');
                this.connectionStatus = document.getElementById('connection-status');
                this.typingIndicator = document.getElementById('typing-indicator');
                this.isStreaming = false;
                
                this.setupEventListeners();
                this.connect();
            }
            
            setupEventListeners() {
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
            }
            
            connect() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.updateConnectionStatus('Connected', 'success');
                    this.messageInput.disabled = false;
                    this.messageInput.focus();
                };
                
                this.ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                };
                
                this.ws.onclose = () => {
                    this.updateConnectionStatus('Disconnected', 'error');
                    this.messageInput.disabled = true;
                    setTimeout(() => this.connect(), 3000);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.updateConnectionStatus('Connection Error', 'error');
                };
            }
            
            updateConnectionStatus(text, type) {
                this.connectionStatus.textContent = text;
                this.connectionStatus.className = `status ${type}`;
            }
            
            sendMessage() {
                const text = this.messageInput.value.trim();
                if (!text || !this.ws || this.ws.readyState !== WebSocket.OPEN) return;
                
                // Add user message to UI
                this.addMessage('user', 'You', text);
                
                // Send to server
                if (this.isStreaming) {
                    this.ws.send(JSON.stringify({ type: 'interrupt', text }));
                } else {
                    this.ws.send(JSON.stringify({ type: 'chat', text }));
                }
                
                this.messageInput.value = '';
                this.showTyping();
            }
            
            handleMessage(message) {
                switch (message.type) {
                    case 'connected':
                        console.log('Connected with session ID:', message.sessionId);
                        break;
                        
                    case 'chat_start':
                        this.isStreaming = true;
                        this.addMessage('system', 'System', `--- ${message.theme} ---`);
                        break;
                        
                    case 'message':
                        this.addMessage('assistant', message.character, message.text);
                        break;
                        
                    case 'chat_complete':
                        this.isStreaming = false;
                        this.hideTyping();
                        if (message.skills && message.skills.length > 0) {
                            this.addSkills(message.skills);
                        }
                        break;
                        
                    case 'interrupted':
                        this.addMessage('system', 'System', message.message);
                        break;
                        
                    case 'error':
                        this.isStreaming = false;
                        this.hideTyping();
                        this.addMessage('error', 'Error', `${message.error}${message.details ? ': ' + message.details : ''}`);
                        break;
                }
            }
            
            addMessage(type, character, text) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                
                if (type === 'assistant') {
                    messageDiv.innerHTML = `
                        <div class="character-name">${character}</div>
                        <div>${text}</div>
                    `;
                } else {
                    messageDiv.textContent = text;
                }
                
                this.messagesContainer.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            addSkills(skills) {
                const skillsDiv = document.createElement('div');
                skillsDiv.className = 'skills';
                skillsDiv.textContent = `Skills: ${skills.join(', ')}`;
                this.messagesContainer.appendChild(skillsDiv);
                this.scrollToBottom();
            }
            
            showTyping() {
                this.typingIndicator.classList.add('show');
            }
            
            hideTyping() {
                this.typingIndicator.classList.remove('show');
            }
            
            scrollToBottom() {
                this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
            }
        }
        
        // Initialize the chat UI when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DelayedChatUI();
        });
    </script>
</body>
</html>
