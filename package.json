{"name": "forachat", "version": "1.0.0", "description": "Group text for professional interpersonal skills enrichment", "homepage": "https://github.com/askfora/forachat#readme", "bugs": {"url": "https://github.com/askfora/forachat/issues"}, "repository": {"type": "git", "url": "git+https://github.com/askfora/forachat.git"}, "license": "ISC", "author": "<EMAIL>", "type": "commonjs", "main": "dist/index.js", "scripts": {"test": "test/index.js", "start": "npm run build && node dist/index.js", "build": "tsc", "migrate": "npx knex migrate:latest", "repl": "npm run build && node dist/repl.js"}, "dependencies": {"@dbos-inc/dbos-cloud": "^2.10.24", "@dbos-inc/dbos-sdk": "^2.10.24", "@google/generative-ai": "^0.24.1", "dotenv": "^17.0.1", "express": "^5.1.0", "knex": "^3.1.0", "pg": "^8.12.0"}, "devDependencies": {"@dbos-inc/dbos-sdk": "^2.10.24", "@types/express": "^5.0.3", "@types/koa__router": "^12.0.4", "@types/pg": "^8.15.4", "@types/ws": "^8.18.1", "knex": "^3.1.0", "typescript": "^5.8.3"}}