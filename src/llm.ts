import { GoogleGenerativeAI } from "@google/generative-ai";
import { DBOS } from "@dbos-inc/dbos-sdk";

export class LLM {
    @DBOS.step()
    static async generate(systemPrompt: string, userPrompt: string): Promise<any> {
        const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
        const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

        const chat = model.startChat({
            history: [
                {
                    role: "user",
                    parts: [{ text: systemPrompt }],
                },
                {
                    role: "model",
                    parts: [{ text: "Okay, I'm ready. What's the user's request?" }],
                },
            ],
            generationConfig: {
                maxOutputTokens: 8192,
                responseMimeType: "application/json",
            },
        });

        const result = await chat.sendMessage(userPrompt);
        const response = result.response;
        const text = response.text();

        DBOS.logger.info(`Raw LLM response text: ${text}`);

        try {
            const parsed = JSON.parse(text);
            DBOS.logger.info(`Parsed LLM response: ${JSON.stringify(parsed)}`);
            return parsed;
        } catch (error) {
            DBOS.logger.error(`Failed to parse LLM response as JSON: ${(error as Error).message}`);
            DBOS.logger.error(`Raw response was: ${text}`);
            throw new Error(`Invalid JSON response from LLM: ${(error as Error).message}`);
        }
    }
}