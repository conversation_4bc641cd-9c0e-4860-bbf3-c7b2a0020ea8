import readline from 'readline';
import http from 'http';

interface DelayedMessage {
  character: string;
  text: string;
  delay: number;
}

interface ChatResponse {
  reply: DelayedMessage[];
  theme?: string;
  skills?: string[];
  error?: string;
  details?: string;
}

class DelayedREPL {
  private rl: readline.Interface;
  private isDisplayingMessages = false;
  private currentTimeouts: NodeJS.Timeout[] = [];
  private interrupted = false;
  private pendingUserInput: string | null = null;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: '> '
    });

    this.setupEventHandlers();
    this.rl.prompt();
  }

  private setupEventHandlers() {
    this.rl.on('line', (line) => {
      const userInput = line.trim();

      if (this.isDisplayingMessages) {
        // User interrupted while messages are being displayed
        this.interrupted = true;
        this.pendingUserInput = userInput;
        this.clearTimeouts();
        console.log('\n[Interrupted] Processing your new message...\n');
        return;
      }

      this.sendChatMessage(userInput);
    });

    this.rl.on('close', () => {
      console.log('Have a great day!');
      process.exit(0);
    });
  }

  private clearTimeouts() {
    this.currentTimeouts.forEach(timeout => clearTimeout(timeout));
    this.currentTimeouts = [];
  }

  private async sendChatMessage(text: string) {
    const postData = JSON.stringify({ text });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response: ChatResponse = JSON.parse(data);
          this.handleChatResponse(response);
        } catch (error) {
          console.error('Failed to parse response:', error);
          console.error('Raw response:', data);
          this.rl.prompt();
        }
      });
    });

    req.on('error', (e) => {
      console.error(`problem with request: ${e.message}`);
      this.rl.prompt();
    });

    req.write(postData);
    req.end();
  }

  private handleChatResponse(response: ChatResponse) {
    // Handle error responses
    if (response.error) {
      console.log(`Error: ${response.error}`);
      if (response.details) {
        console.log(`Details: ${response.details}`);
      }
      this.rl.prompt();
      return;
    }

    // Handle successful responses with reply array
    if (response.reply && Array.isArray(response.reply)) {
      console.log(`\n--- ${response.theme || 'Chat Response'} ---`);
      this.displayDelayedMessages(response.reply, response.skills);
    } else {
      console.log('Unexpected response format:', JSON.stringify(response, null, 2));
      this.rl.prompt();
    }
  }

  private displayDelayedMessages(messages: DelayedMessage[], skills?: string[]) {
    this.isDisplayingMessages = true;
    this.interrupted = false;
    this.clearTimeouts();

    let cumulativeDelay = 0;

    messages.forEach((message, index) => {
      const delay = message.delay || 0;
      cumulativeDelay += delay * 1000; // Convert seconds to milliseconds

      const timeout = setTimeout(() => {
        if (this.interrupted) {
          return; // Don't display if interrupted
        }

        console.log(`${message.character}: ${message.text}`);

        // If this is the last message, show skills and prompt
        if (index === messages.length - 1) {
          if (skills && skills.length > 0) {
            console.log(`\nSkills: ${skills.join(', ')}`);
          }
          console.log(''); // Empty line for readability
          this.isDisplayingMessages = false;

          // Check if user interrupted and has pending input
          if (this.interrupted && this.pendingUserInput) {
            const pendingInput = this.pendingUserInput;
            this.pendingUserInput = null;
            this.sendChatMessage(pendingInput);
          } else {
            this.rl.prompt();
          }
        }
      }, cumulativeDelay);

      this.currentTimeouts.push(timeout);
    });
  }
}

// Create and start the REPL
new DelayedREPL();
