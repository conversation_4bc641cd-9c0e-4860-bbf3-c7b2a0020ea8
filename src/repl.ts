import readline from 'readline';
import http from 'http';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  prompt: '> '
});

rl.prompt();

rl.on('line', (line) => {
  const postData = JSON.stringify({ text: line.trim() });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/chat',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      try {
        const response = JSON.parse(data);

        // Handle error responses
        if (response.error) {
          console.log(`Error: ${response.error}`);
          if (response.details) {
            console.log(`Details: ${response.details}`);
          }
          rl.prompt();
          return;
        }

        // Handle successful responses with reply array
        if (response.reply && Array.isArray(response.reply)) {
          console.log(`\n--- ${response.theme || 'Chat Response'} ---`);
          response.reply.forEach((message: any, index: number) => {
            console.log(`${message.character}: ${message.text}`);
          });
          if (response.skills && response.skills.length > 0) {
            console.log(`\nSkills: ${response.skills.join(', ')}`);
          }
          console.log(''); // Empty line for readability
        } else {
          console.log('Unexpected response format:', JSON.stringify(response, null, 2));
        }

        rl.prompt();
      } catch (error) {
        console.error('Failed to parse response:', error);
        console.error('Raw response:', data);
        rl.prompt();
      }
    });
  });

  req.on('error', (e) => {
    console.error(`problem with request: ${e.message}`);
    rl.prompt();
  });

  req.write(postData);
  req.end();
}).on('close', () => {
  console.log('Have a great day!');
  process.exit(0);
});
