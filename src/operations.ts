import { DBOS } from '@dbos-inc/dbos-sdk';
import { LLM } from './llm';
import * as fs from 'fs/promises';
import * as path from 'path';

// Interface definitions remain the same...
interface Conversation {
    id: number;
    created_at: Date;
    updated_at: Date;
}

interface Message {
    id: number;
    character: string;
    text: string;
    conversation_id: number;
    created_at: Date;
    updated_at: Date;
}

export class ForaChat {

    @DBOS.transaction()
    static async createConversation(): Promise<Conversation> {
        const [conversation] = await DBOS.knexClient<Conversation>('forachat.conversations').insert({}).returning('*');
        return conversation;
    }

    @DBOS.transaction()
    static async addMessage(character: string, text: string, conversation_id: number): Promise<Message> {
        const [message] = await DBOS.knexClient<Message>('forachat.messages').insert({
            character,
            text,
            conversation_id
        }).returning('*');
        return message;
    }

    @DBOS.step()
    static async getSystemPrompt(): Promise<string> {
        const filePath = path.join(__dirname, '..', 'prompts', 'agent_system.md');
        return fs.readFile(filePath, 'utf-8');
    }

    @DBOS.workflow()
    static async chatWorkflow(userMessage: string, conversationId?: number): Promise<any> {
        let conversation;
        if (conversationId) {
            // Use existing conversation
            conversation = { id: conversationId };
        } else {
            // Create new conversation
            conversation = await ForaChat.createConversation();
        }

        await ForaChat.addMessage("user", userMessage, conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();
        const llmResponse = await LLM.generate(systemPrompt, userMessage);

        DBOS.logger.info(`LLM Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply) || llmResponse.reply.length === 0) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or empty reply array");
        }

        // Store all response messages
        for (const responseMessage of llmResponse.reply) {
            await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
        }

        return { ...llmResponse, conversationId: conversation.id };
    }

    @DBOS.workflow()
    static async interruptedChatWorkflow(
        userMessage: string,
        previousMessages: Array<{character: string, text: string}>,
        conversationId?: number
    ): Promise<any> {
        let conversation;
        if (conversationId) {
            conversation = { id: conversationId };
        } else {
            conversation = await ForaChat.createConversation();
        }

        // Build context from previous messages and new user input
        const contextMessages = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');
        const fullPrompt = `Previous conversation:\n${contextMessages}\n\nUser (interrupting): ${userMessage}`;

        await ForaChat.addMessage("user", userMessage, conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();
        const llmResponse = await LLM.generate(systemPrompt, fullPrompt);

        DBOS.logger.info(`Interrupted LLM Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply) || llmResponse.reply.length === 0) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or empty reply array");
        }

        // Store all response messages
        for (const responseMessage of llmResponse.reply) {
            await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
        }

        return { ...llmResponse, conversationId: conversation.id };
    }
}
