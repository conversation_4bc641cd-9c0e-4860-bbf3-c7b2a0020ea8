import { DBOS } from '@dbos-inc/dbos-sdk';
import { LLM } from './llm';
import * as fs from 'fs/promises';
import * as path from 'path';

// Interface definitions remain the same...
interface Conversation {
    id: number;
    created_at: Date;
    updated_at: Date;
}

interface Message {
    id: number;
    character: string;
    text: string;
    conversation_id: number;
    created_at: Date;
    updated_at: Date;
}

export class ForaChat {

    @DBOS.transaction()
    static async createConversation(): Promise<Conversation> {
        const [conversation] = await DBOS.knexClient<Conversation>('forachat.conversations').insert({}).returning('*');
        return conversation;
    }

    @DBOS.transaction()
    static async addMessage(character: string, text: string, conversation_id: number): Promise<Message> {
        const [message] = await DBOS.knexClient<Message>('forachat.messages').insert({
            character,
            text,
            conversation_id
        }).returning('*');
        return message;
    }

    @DBOS.step()
    static async getSystemPrompt(): Promise<string> {
        const filePath = path.join(__dirname, '..', 'prompts', 'agent_system.md');
        return fs.readFile(filePath, 'utf-8');
    }

    @DBOS.workflow()
    static async chatWorkflow(userMessage: string): Promise<any> {
        const conversation = await ForaChat.createConversation();
        await ForaChat.addMessage("user", userMessage, conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();
        const llmResponse = await LLM.generate(systemPrompt, userMessage);

        DBOS.logger.info(`LLM Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply) || llmResponse.reply.length === 0) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or empty reply array");
        }

        const responseMessage = llmResponse.reply[0];
        await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
        return llmResponse; // Return the full response instead of just the first message
    }
}
